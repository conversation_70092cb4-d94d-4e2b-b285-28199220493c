-- Invoice Table Schema
-- Contains invoice information with project-level access control
-- Invoices are associated with projects and vendors
CREATE TABLE IF NOT EXISTS "public"."purchase_order" (
	"invoice_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"po_number" "text" NOT NULL,
	"vendor_id" "uuid" NOT NULL,
	"description" "text",
	"invoice_date" "date" NOT NULL,
	"account" "text" NOT NULL,
	"amount" numeric(15, 2) NOT NULL,
	"period" "text",
	"post_date" "date" NOT NULL,
	"notes" "text",
	-- Project ID is not nullable since invoices are project-specific and 
	-- vendors may be associated with a client or organization
	"project_id" "uuid" NOT NULL,
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);
